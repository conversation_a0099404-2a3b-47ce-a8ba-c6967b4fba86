# Nested Layouts Demo với Next.js Pages Router

Dự án này demo cách implement **nested layouts** trong Next.js Pages Router sử dụng **Higher-Order Components (HOCs)** pattern.

## 🎯 Mục đích

Ví dụ này minh họa cách tạo và sử dụng nested layouts một cách linh hoạt trong Next.js Pages Router, bao gồm:

- **MainLayout**: Layout chính cho toàn bộ ứng dụng
- **DashboardLayout**: Sub-layout cho khu vực dashboard
- **AdminLayout**: Sub-layout cho khu vực admin
- **HOCs Composition**: Kết hợp nhiều layouts thông qua HOCs
- **Conditional Layouts**: Áp dụng layouts dựa trên điều kiện
- **TypeScript Support**: Hỗ trợ TypeScript với type safety

## 🚀 Demo Pages

1. **Home** (`/home`) - Chỉ MainLayout
2. **Dashboard** (`/dashboard`) - MainLayout + DashboardLayout
3. **Analytics** (`/dashboard/analytics`) - MainLayout + DashboardLayout
4. **Reports** (`/dashboard/reports`) - Demo utility functions
5. **Admin** (`/admin`) - MainLayout + AdminLayout
6. **User Management** (`/admin/users`) - MainLayout + AdminLayout
7. **Profile** (`/profile`) - Demo conditional HOC

## 🏗️ Cấu trúc

```
├── components/layouts/     # Layout components
├── hocs/                  # Higher-Order Components
├── utils/                 # Utility functions
├── types/                 # TypeScript types
├── pages/                 # Demo pages
└── styles/               # CSS modules
```

## 🔧 Getting Started

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

Mở [http://localhost:3000](http://localhost:3000) để xem demo.

## 📚 Tài liệu chi tiết

Xem file [NESTED_LAYOUTS_README.md](./NESTED_LAYOUTS_README.md) để biết thêm chi tiết về:
- Cách hoạt động của HOCs
- Patterns và best practices
- TypeScript integration
- Utility functions

## ✨ Tính năng chính

- ✅ **Flexible Layout Composition**: Kết hợp layouts dễ dàng
- ✅ **Reusable HOCs**: Tái sử dụng logic layout
- ✅ **TypeScript Support**: Type safety cho HOCs
- ✅ **Utility Functions**: Helpers để compose HOCs
- ✅ **Conditional Layouts**: Layouts dựa trên điều kiện
- ✅ **Responsive Design**: Tất cả layouts đều responsive
- ✅ **Educational**: Code được comment chi tiết

## 🎨 Styling

Sử dụng CSS Modules với responsive design và modern UI patterns.

## 📖 Learn More

- [Next.js Pages Router](https://nextjs.org/docs/pages)
- [React Higher-Order Components](https://reactjs.org/docs/higher-order-components.html)
- [CSS Modules](https://github.com/css-modules/css-modules)
