import React from 'react';
import withMainLayout from '../../hocs/withMainLayout';
import withAdminLayout from '../../hocs/withAdminLayout';
import styles from '../../styles/Page.module.css';

/**
 * AdminPage - Trang admin với nested layouts
 * Sử dụng cả MainLayout và AdminLayout thông qua HOC composition
 */
const AdminPage = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Admin Panel</h1>
        <p className={styles.pageSubtitle}>
          System administration and management tools
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔐 Admin Layout Demo</h2>
          <p className={styles.cardDescription}>
            Trang này sử dụng AdminLayout thay vì DashboardLayout:
          </p>
          <ul className={styles.featureList}>
            <li>✅ MainLayout (Header, Navigation, Footer)</li>
            <li>✅ AdminLayout (Admin Header, Admin Sidebar)</li>
            <li>✅ Admin Content (Nội dung admin)</li>
          </ul>
          <div className={styles.codeExample}>
            <code>
              export default withMainLayout(withAdminLayout(AdminPage));
            </code>
          </div>
        </div>

        <div className={styles.adminGrid}>
          <div className={styles.adminCard}>
            <h3 className={styles.adminCardTitle}>👥 User Management</h3>
            <p className={styles.adminCardDescription}>
              Manage user accounts, permissions, and access levels
            </p>
            <div className={styles.adminStats}>
              <span>Total Users: 1,234</span>
              <span>Active: 1,156</span>
              <span>Pending: 78</span>
            </div>
            <a href="/admin/users" className={styles.adminButton}>
              Manage Users
            </a>
          </div>

          <div className={styles.adminCard}>
            <h3 className={styles.adminCardTitle}>📝 Content Management</h3>
            <p className={styles.adminCardDescription}>
              Create, edit, and publish content across the platform
            </p>
            <div className={styles.adminStats}>
              <span>Published: 456</span>
              <span>Drafts: 23</span>
              <span>Scheduled: 12</span>
            </div>
            <a href="/admin/content" className={styles.adminButton}>
              Manage Content
            </a>
          </div>

          <div className={styles.adminCard}>
            <h3 className={styles.adminCardTitle}>🔧 System Settings</h3>
            <p className={styles.adminCardDescription}>
              Configure system-wide settings and preferences
            </p>
            <div className={styles.adminStats}>
              <span>Uptime: 99.9%</span>
              <span>Load: 23%</span>
              <span>Memory: 67%</span>
            </div>
            <a href="/admin/system" className={styles.adminButton}>
              System Settings
            </a>
          </div>

          <div className={styles.adminCard}>
            <h3 className={styles.adminCardTitle}>📜 System Logs</h3>
            <p className={styles.adminCardDescription}>
              View and analyze system logs and error reports
            </p>
            <div className={styles.adminStats}>
              <span>Errors: 3</span>
              <span>Warnings: 12</span>
              <span>Info: 1,234</span>
            </div>
            <a href="/admin/logs" className={styles.adminButton}>
              View Logs
            </a>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>⚠️ Recent Alerts</h2>
          <div className={styles.alertsList}>
            <div className={styles.alert}>
              <span className={styles.alertIcon}>🔴</span>
              <div>
                <h4>High Memory Usage</h4>
                <p>Server memory usage is at 87%. Consider scaling resources.</p>
                <small>2 minutes ago</small>
              </div>
            </div>
            <div className={styles.alert}>
              <span className={styles.alertIcon}>🟡</span>
              <div>
                <h4>Backup Completed</h4>
                <p>Daily backup completed successfully. 2.3GB backed up.</p>
                <small>1 hour ago</small>
              </div>
            </div>
            <div className={styles.alert}>
              <span className={styles.alertIcon}>🟢</span>
              <div>
                <h4>Security Scan Clean</h4>
                <p>Weekly security scan completed. No threats detected.</p>
                <small>3 hours ago</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// HOC Composition với AdminLayout thay vì DashboardLayout
export default withMainLayout(withAdminLayout(AdminPage));
