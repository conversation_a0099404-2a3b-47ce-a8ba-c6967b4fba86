import React from 'react';
import withMainLayout from '../../hocs/withMainLayout';
import withAdminLayout from '../../hocs/withAdminLayout';
import styles from '../../styles/Page.module.css';

/**
 * AdminUsersPage - Trang quản lý users với nested layouts
 * Cũng sử dụng cả MainLayout và AdminLayout như admin index
 */
const AdminUsersPage = () => {
  // Mock data cho demo
  const users = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Moderator', status: 'Inactive' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Pending' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active' },
  ];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>User Management</h1>
        <p className={styles.pageSubtitle}>
          Manage user accounts, roles, and permissions
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>👥 Consistent Admin Layout</h2>
          <p className={styles.cardDescription}>
            Trang này cũng sử dụng cùng nested layout structure như admin index:
          </p>
          <ul className={styles.featureList}>
            <li>✅ MainLayout (Consistent across all pages)</li>
            <li>✅ AdminLayout (Consistent across admin section)</li>
            <li>✅ User Management Content (Specific to this page)</li>
          </ul>
        </div>

        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>User List</h2>
            <button className={styles.primaryButton}>Add New User</button>
          </div>
          
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id}>
                    <td>{user.id}</td>
                    <td>{user.name}</td>
                    <td>{user.email}</td>
                    <td>
                      <span className={`${styles.badge} ${styles[`badge${user.role}`]}`}>
                        {user.role}
                      </span>
                    </td>
                    <td>
                      <span className={`${styles.status} ${styles[`status${user.status}`]}`}>
                        {user.status}
                      </span>
                    </td>
                    <td>
                      <div className={styles.actionButtons}>
                        <button className={styles.editButton}>Edit</button>
                        <button className={styles.deleteButton}>Delete</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Total Users</h3>
            <p className={styles.statValue}>1,234</p>
            <p className={styles.statChange}>+45 this month</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Active Users</h3>
            <p className={styles.statValue}>1,156</p>
            <p className={styles.statChange}>93.7% of total</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>New Registrations</h3>
            <p className={styles.statValue}>78</p>
            <p className={styles.statChange}>This week</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Admin Users</h3>
            <p className={styles.statValue}>12</p>
            <p className={styles.statChange}>1% of total</p>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔧 User Management Tools</h2>
          <div className={styles.toolsGrid}>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>📊</span>
              Export User Data
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>📧</span>
              Send Bulk Email
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🔒</span>
              Reset Passwords
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🗑️</span>
              Bulk Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Cùng HOC composition như admin index
export default withMainLayout(withAdminLayout(AdminUsersPage));
