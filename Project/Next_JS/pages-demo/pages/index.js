import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

/**
 * Index page - Redirect to home page để demo nested layouts
 */
export default function Index() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to home page
    router.push('/home');
  }, [router]);

  return (
    <>
      <Head>
        <title>Nested Layouts Demo</title>
        <meta name="description" content="Demo nested layouts với HOCs trong Next.js Pages Router" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h1>🔄 Redirecting to Nested Layouts Demo...</h1>
          <p>Bạn sẽ được chuyển hướng đến trang home để xem demo nested layouts.</p>
        </div>
      </main>
    </>
  );
}
