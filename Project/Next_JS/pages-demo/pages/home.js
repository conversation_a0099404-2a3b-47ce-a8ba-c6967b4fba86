import React from 'react';
import withMainLayout from '../hocs/withMainLayout';
import styles from '../styles/Page.module.css';

/**
 * HomePage - Trang chủ chỉ sử dụng MainLayout
 * Đ<PERSON>y là ví dụ về trang đơn giản chỉ cần layout chính
 */
const HomePage = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Welcome to MyApp</h1>
        <p className={styles.pageSubtitle}>
          This is the home page with only the main layout
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🏠 Main Layout Only</h2>
          <p className={styles.cardDescription}>
            Trang này chỉ sử dụng MainLayout thông qua HOC withMainLayout.
            Bạn có thể thấy header, navigation và footer từ MainLayout.
          </p>
          <ul className={styles.featureList}>
            <li>✅ Header với navigation</li>
            <li>✅ Main content area</li>
            <li>✅ Footer với links</li>
            <li>❌ Không có sub-layout</li>
          </ul>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🚀 Features</h2>
          <p className={styles.cardDescription}>
            Explore different sections of the application:
          </p>
          <div className={styles.buttonGroup}>
            <a href="/dashboard" className={styles.button}>
              Go to Dashboard
            </a>
            <a href="/admin" className={styles.button}>
              Go to Admin Panel
            </a>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>📚 About Nested Layouts</h2>
          <p className={styles.cardDescription}>
            Ứng dụng này demo cách sử dụng nested layouts với HOCs trong Next.js Pages Router:
          </p>
          <ul className={styles.featureList}>
            <li><strong>MainLayout:</strong> Layout chính cho toàn bộ app</li>
            <li><strong>DashboardLayout:</strong> Sub-layout cho dashboard pages</li>
            <li><strong>AdminLayout:</strong> Sub-layout cho admin pages</li>
            <li><strong>HOCs:</strong> Higher-Order Components để compose layouts</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Export trang đã được wrap với MainLayout
export default withMainLayout(HomePage);
