import React, { useState } from 'react';
import { composeHOCs, conditionalHOC } from '../utils/composeHOCs';
import withMainLayout from '../hocs/withMainLayout';
import withAdminLayout from '../hocs/withAdminLayout';
import styles from '../styles/Page.module.css';

/**
 * ProfilePage - Demo conditional HOC
 * Trang này sẽ hiển thị AdminLayout nếu user là admin,
 * ngược lại chỉ hiển thị MainLayout
 */
const ProfilePage = ({ user = { role: 'user', name: '<PERSON>' } }) => {
  const [currentUser, setCurrentUser] = useState(user);

  const toggleUserRole = () => {
    setCurrentUser(prev => ({
      ...prev,
      role: prev.role === 'admin' ? 'user' : 'admin'
    }));
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>User Profile</h1>
        <p className={styles.pageSubtitle}>
          Manage your profile and account settings
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔀 Conditional Layout Demo</h2>
          <p className={styles.cardDescription}>
            Trang này sử dụng conditional HOC để hiển thị layout khác nhau 
            dựa trên role của user:
          </p>
          <ul className={styles.featureList}>
            <li><strong>Admin user:</strong> MainLayout + AdminLayout</li>
            <li><strong>Regular user:</strong> Chỉ MainLayout</li>
          </ul>
          
          <div className={styles.codeExample}>
            <code>
              const withConditionalAdmin = conditionalHOC(<br/>
              &nbsp;&nbsp;(props) => props.user?.role === 'admin',<br/>
              &nbsp;&nbsp;withAdminLayout<br/>
              );<br/><br/>
              export default composeHOCs(<br/>
              &nbsp;&nbsp;withMainLayout,<br/>
              &nbsp;&nbsp;withConditionalAdmin<br/>
              )(ProfilePage);
            </code>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>👤 Current User Info</h2>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <h3 className={styles.statTitle}>Name</h3>
              <p className={styles.statValue}>{currentUser.name}</p>
            </div>
            
            <div className={styles.statCard}>
              <h3 className={styles.statTitle}>Role</h3>
              <p className={styles.statValue}>
                <span className={`${styles.badge} ${
                  currentUser.role === 'admin' ? styles.badgeAdmin : styles.badgeUser
                }`}>
                  {currentUser.role}
                </span>
              </p>
            </div>
            
            <div className={styles.statCard}>
              <h3 className={styles.statTitle}>Layout</h3>
              <p className={styles.statValue}>
                {currentUser.role === 'admin' ? 'Main + Admin' : 'Main Only'}
              </p>
            </div>
            
            <div className={styles.statCard}>
              <h3 className={styles.statTitle}>Access Level</h3>
              <p className={styles.statValue}>
                {currentUser.role === 'admin' ? 'Full Access' : 'Limited'}
              </p>
            </div>
          </div>

          <div className={styles.buttonGroup}>
            <button 
              className={styles.button}
              onClick={toggleUserRole}
            >
              Toggle Role ({currentUser.role === 'admin' ? 'Make User' : 'Make Admin'})
            </button>
            <button className={styles.button}>
              Refresh Page to See Layout Change
            </button>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>⚙️ Profile Settings</h2>
          <div className={styles.toolsGrid}>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>👤</span>
              Edit Profile
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🔒</span>
              Change Password
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🔔</span>
              Notifications
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🎨</span>
              Preferences
            </button>
            {currentUser.role === 'admin' && (
              <>
                <button className={styles.toolButton}>
                  <span className={styles.toolIcon}>🔧</span>
                  Admin Settings
                </button>
                <button className={styles.toolButton}>
                  <span className={styles.toolIcon}>👥</span>
                  Manage Users
                </button>
              </>
            )}
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>📊 Activity Summary</h2>
          <div className={styles.insightsList}>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>📅</span>
              <div>
                <h4>Last Login</h4>
                <p>Today at 2:30 PM from Chrome on Windows</p>
              </div>
            </div>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>📈</span>
              <div>
                <h4>Profile Views</h4>
                <p>Your profile has been viewed 23 times this month</p>
              </div>
            </div>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>🎯</span>
              <div>
                <h4>Achievements</h4>
                <p>You've completed 8 out of 10 profile setup tasks</p>
              </div>
            </div>
            {currentUser.role === 'admin' && (
              <div className={styles.insight}>
                <span className={styles.insightIcon}>🔐</span>
                <div>
                  <h4>Admin Actions</h4>
                  <p>You've performed 15 admin actions this week</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>💡 Layout Explanation</h2>
          <p className={styles.cardDescription}>
            Conditional HOCs cho phép bạn áp dụng layouts khác nhau dựa trên:
          </p>
          <ul className={styles.featureList}>
            <li>User role và permissions</li>
            <li>Feature flags</li>
            <li>Device type (mobile/desktop)</li>
            <li>User preferences</li>
            <li>A/B testing variants</li>
            <li>Authentication status</li>
          </ul>
          <p className={styles.cardDescription}>
            Điều này giúp tạo ra trải nghiệm người dùng linh hoạt và cá nhân hóa.
          </p>
        </div>
      </div>
    </div>
  );
};

// Conditional HOC: Chỉ apply AdminLayout nếu user là admin
const withConditionalAdmin = conditionalHOC(
  (props) => props.user?.role === 'admin',
  withAdminLayout
);

// Compose HOCs: MainLayout luôn có, AdminLayout chỉ khi cần
export default composeHOCs(
  withMainLayout,
  withConditionalAdmin
)(ProfilePage);
