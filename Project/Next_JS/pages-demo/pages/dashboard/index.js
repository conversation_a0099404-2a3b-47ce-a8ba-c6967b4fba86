import React from 'react';
import withMainLayout from '../../hocs/withMainLayout';
import withDashboardLayout from '../../hocs/withDashboardLayout';
import styles from '../../styles/Page.module.css';

/**
 * DashboardPage - Trang dashboard với nested layouts
 * Sử dụng cả MainLayout và DashboardLayout thông qua HOC composition
 */
const DashboardPage = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Dashboard Overview</h1>
        <p className={styles.pageSubtitle}>
          Welcome to your dashboard with nested layouts
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>📊 Nested Layout Demo</h2>
          <p className={styles.cardDescription}>
            Trang này sử dụng nested layouts thông qua HOC composition:
          </p>
          <ul className={styles.featureList}>
            <li>✅ MainLayout (Header, Navigation, Footer)</li>
            <li>✅ DashboardLayout (Dashboard Header, Sidebar)</li>
            <li>✅ Page Content (Nội dung trang này)</li>
          </ul>
          <div className={styles.codeExample}>
            <code>
              export default withMainLayout(withDashboardLayout(DashboardPage));
            </code>
          </div>
        </div>

        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Total Users</h3>
            <p className={styles.statValue}>1,234</p>
            <p className={styles.statChange}>+12% from last month</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Revenue</h3>
            <p className={styles.statValue}>$45,678</p>
            <p className={styles.statChange}>+8% from last month</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Orders</h3>
            <p className={styles.statValue}>567</p>
            <p className={styles.statChange}>+15% from last month</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Conversion Rate</h3>
            <p className={styles.statValue}>3.2%</p>
            <p className={styles.statChange}>+0.5% from last month</p>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔗 Quick Actions</h2>
          <div className={styles.buttonGroup}>
            <a href="/dashboard/analytics" className={styles.button}>
              View Analytics
            </a>
            <a href="/dashboard/reports" className={styles.button}>
              Generate Reports
            </a>
            <a href="/dashboard/settings" className={styles.button}>
              Dashboard Settings
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

// HOC Composition: Kết hợp cả MainLayout và DashboardLayout
// Thứ tự quan trọng: withMainLayout bên ngoài, withDashboardLayout bên trong
export default withMainLayout(withDashboardLayout(DashboardPage));
