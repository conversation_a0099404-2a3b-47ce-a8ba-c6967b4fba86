import React from 'react';
import { withDashboardLayouts } from '../../utils/composeHOCs';
import styles from '../../styles/Page.module.css';

/**
 * ReportsPage - Demo sử dụng utility function composeHOCs
 * Thay vì withMainLayout(withDashboardLayout(ReportsPage))
 * <PERSON><PERSON>g ta sử dụng withDashboardLayouts từ utility
 */
const ReportsPage = () => {
  // Mock data cho reports
  const reports = [
    {
      id: 1,
      name: 'Monthly Sales Report',
      type: 'Sales',
      status: 'Generated',
      date: '2024-01-15',
      size: '2.3 MB'
    },
    {
      id: 2,
      name: 'User Activity Report',
      type: 'Analytics',
      status: 'Generating',
      date: '2024-01-14',
      size: '1.8 MB'
    },
    {
      id: 3,
      name: 'Financial Summary Q4',
      type: 'Financial',
      status: 'Generated',
      date: '2024-01-13',
      size: '4.1 MB'
    },
    {
      id: 4,
      name: 'Performance Metrics',
      type: 'Performance',
      status: 'Failed',
      date: '2024-01-12',
      size: '0 MB'
    }
  ];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Reports Dashboard</h1>
        <p className={styles.pageSubtitle}>
          Generate and manage your business reports
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔧 Composed HOCs Demo</h2>
          <p className={styles.cardDescription}>
            Trang này sử dụng utility function <code>withDashboardLayouts</code> 
            thay vì compose HOCs manually:
          </p>
          <div className={styles.codeExample}>
            <code>
              // Thay vì:<br/>
              export default withMainLayout(withDashboardLayout(ReportsPage));<br/><br/>
              // Chúng ta sử dụng:<br/>
              export default withDashboardLayouts(ReportsPage);
            </code>
          </div>
          <ul className={styles.featureList}>
            <li>✅ Code ngắn gọn và dễ đọc hơn</li>
            <li>✅ Tái sử dụng layout combinations</li>
            <li>✅ Dễ dàng thay đổi layout structure</li>
            <li>✅ Consistent across multiple pages</li>
          </ul>
        </div>

        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>📊 Available Reports</h2>
            <button className={styles.primaryButton}>Generate New Report</button>
          </div>
          
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>Report Name</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Date</th>
                  <th>Size</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {reports.map(report => (
                  <tr key={report.id}>
                    <td>{report.name}</td>
                    <td>
                      <span className={`${styles.badge} ${styles[`badge${report.type}`]}`}>
                        {report.type}
                      </span>
                    </td>
                    <td>
                      <span className={`${styles.status} ${styles[`status${report.status}`]}`}>
                        {report.status}
                      </span>
                    </td>
                    <td>{report.date}</td>
                    <td>{report.size}</td>
                    <td>
                      <div className={styles.actionButtons}>
                        {report.status === 'Generated' && (
                          <>
                            <button className={styles.editButton}>Download</button>
                            <button className={styles.editButton}>View</button>
                          </>
                        )}
                        {report.status === 'Failed' && (
                          <button className={styles.editButton}>Retry</button>
                        )}
                        <button className={styles.deleteButton}>Delete</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Total Reports</h3>
            <p className={styles.statValue}>156</p>
            <p className={styles.statChange}>+12 this month</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Generated Today</h3>
            <p className={styles.statValue}>8</p>
            <p className={styles.statChange}>+3 from yesterday</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Storage Used</h3>
            <p className={styles.statValue}>234 MB</p>
            <p className={styles.statChange}>12% of quota</p>
          </div>
          
          <div className={styles.statCard}>
            <h3 className={styles.statTitle}>Failed Reports</h3>
            <p className={styles.statValue}>3</p>
            <p className={styles.statChange}>-2 from last week</p>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>📈 Report Types</h2>
          <div className={styles.toolsGrid}>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>💰</span>
              Sales Reports
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>📊</span>
              Analytics Reports
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>💼</span>
              Financial Reports
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>⚡</span>
              Performance Reports
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>👥</span>
              User Reports
            </button>
            <button className={styles.toolButton}>
              <span className={styles.toolIcon}>🔧</span>
              Custom Reports
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Sử dụng utility function thay vì compose HOCs manually
export default withDashboardLayouts(ReportsPage);
