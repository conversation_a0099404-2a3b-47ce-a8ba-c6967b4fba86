import React from 'react';
import withMainLayout from '../../hocs/withMainLayout';
import withDashboardLayout from '../../hocs/withDashboardLayout';
import styles from '../../styles/Page.module.css';

/**
 * AnalyticsPage - Trang analytics với nested layouts
 * Cũng sử dụng cả MainLayout và DashboardLayout như dashboard index
 */
const AnalyticsPage = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Analytics Dashboard</h1>
        <p className={styles.pageSubtitle}>
          Detailed analytics and insights for your business
        </p>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.card}>
          <h2 className={styles.cardTitle}>📈 Same Layout Structure</h2>
          <p className={styles.cardDescription}>
            Trang này cũng sử dụng cùng nested layout structure như dashboard index:
          </p>
          <ul className={styles.featureList}>
            <li>✅ MainLayout (Shared across all pages)</li>
            <li>✅ DashboardLayout (Shared across dashboard section)</li>
            <li>✅ Analytics Content (Specific to this page)</li>
          </ul>
        </div>

        <div className={styles.chartGrid}>
          <div className={styles.chartCard}>
            <h3 className={styles.chartTitle}>📊 Traffic Overview</h3>
            <div className={styles.chartPlaceholder}>
              <p>Chart placeholder - Traffic data visualization</p>
              <div className={styles.chartData}>
                <span>Visitors: 12,345</span>
                <span>Page Views: 45,678</span>
                <span>Bounce Rate: 32%</span>
              </div>
            </div>
          </div>

          <div className={styles.chartCard}>
            <h3 className={styles.chartTitle}>💰 Revenue Trends</h3>
            <div className={styles.chartPlaceholder}>
              <p>Chart placeholder - Revenue trends</p>
              <div className={styles.chartData}>
                <span>This Month: $45,678</span>
                <span>Last Month: $42,123</span>
                <span>Growth: ****%</span>
              </div>
            </div>
          </div>

          <div className={styles.chartCard}>
            <h3 className={styles.chartTitle}>👥 User Demographics</h3>
            <div className={styles.chartPlaceholder}>
              <p>Chart placeholder - User demographics</p>
              <div className={styles.chartData}>
                <span>New Users: 67%</span>
                <span>Returning: 33%</span>
                <span>Mobile: 58%</span>
              </div>
            </div>
          </div>

          <div className={styles.chartCard}>
            <h3 className={styles.chartTitle}>🎯 Conversion Funnel</h3>
            <div className={styles.chartPlaceholder}>
              <p>Chart placeholder - Conversion funnel</p>
              <div className={styles.chartData}>
                <span>Visitors: 10,000</span>
                <span>Leads: 1,200</span>
                <span>Customers: 320</span>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.card}>
          <h2 className={styles.cardTitle}>🔍 Key Insights</h2>
          <div className={styles.insightsList}>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>📱</span>
              <div>
                <h4>Mobile Traffic Increasing</h4>
                <p>Mobile users now account for 58% of total traffic, up 12% from last quarter.</p>
              </div>
            </div>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>⏰</span>
              <div>
                <h4>Peak Hours: 2-4 PM</h4>
                <p>Highest user activity occurs between 2-4 PM on weekdays.</p>
              </div>
            </div>
            <div className={styles.insight}>
              <span className={styles.insightIcon}>🌍</span>
              <div>
                <h4>Top Geographic Markets</h4>
                <p>US (45%), UK (18%), Canada (12%) are the top performing markets.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Cùng HOC composition như dashboard index
export default withMainLayout(withDashboardLayout(AnalyticsPage));
