/**
 * TypeScript types cho nested layouts và HOCs
 * File này demo cách sử dụng TypeScript với HOCs pattern
 */

import { ReactNode, ComponentType } from 'react';
import { NextPage } from 'next';

// Base layout props
export interface LayoutProps {
  children: ReactNode;
}

// Page props interface
export interface PageProps {
  [key: string]: any;
}

// HOC type definition
export type HOC<P = {}> = (
  WrappedComponent: ComponentType<P>
) => ComponentType<P>;

// Layout HOC type
export type LayoutHOC<P = PageProps> = HOC<P>;

// Next.js page with layout
export type NextPageWithLayout<P = PageProps> = NextPage<P> & {
  getLayout?: (page: ReactNode) => ReactNode;
};

// Layout component type
export type LayoutComponent = ComponentType<LayoutProps>;

// Specific layout types
export interface MainLayoutProps extends LayoutProps {
  // Có thể thêm props specific cho MainLayout
}

export interface DashboardLayoutProps extends LayoutProps {
  // <PERSON><PERSON> thể thêm props specific cho DashboardLayout
  dashboardTitle?: string;
}

export interface AdminLayoutProps extends LayoutProps {
  // Có thể thêm props specific cho AdminLayout
  adminLevel?: 'admin' | 'super-admin';
}

// User data types (cho admin pages)
export interface User {
  id: number;
  name: string;
  email: string;
  role: 'Admin' | 'User' | 'Moderator';
  status: 'Active' | 'Inactive' | 'Pending';
}

// Dashboard data types
export interface DashboardStats {
  totalUsers: number;
  revenue: number;
  orders: number;
  conversionRate: number;
}

export interface ChartData {
  label: string;
  value: number;
  change?: number;
}

// Navigation item type
export interface NavItem {
  href: string;
  label: string;
  icon?: string;
}

// Breadcrumb type
export interface BreadcrumbItem {
  href?: string;
  label: string;
  current?: boolean;
}

// Alert type
export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'success' | 'info';
  title: string;
  message: string;
  timestamp: Date;
}

// Export utility type để compose multiple HOCs
export type ComposeHOCs<T> = T extends readonly [infer H, ...infer Rest]
  ? H extends HOC<infer P>
    ? Rest extends readonly []
      ? HOC<P>
      : ComposeHOCs<Rest> extends HOC<infer Q>
      ? HOC<P & Q>
      : never
    : never
  : never;

// Example usage của composed HOCs:
// type MyPageHOCs = ComposeHOCs<[typeof withMainLayout, typeof withDashboardLayout]>;
