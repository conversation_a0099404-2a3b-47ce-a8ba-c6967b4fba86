import React, { ComponentType } from 'react';
import MainLayout from '../components/layouts/MainLayout';
import { LayoutHOC, PageProps } from '../types/layout';

/**
 * withMainLayout - TypeScript version của Higher-Order Component
 * để wrap component với MainLayout
 * 
 * @param WrappedComponent - Component cần được wrap
 * @returns Component đã được wrap với MainLayout
 * 
 * Cách sử dụng:
 * const PageWithLayout = withMainLayout(YourPageComponent);
 * 
 * Với TypeScript:
 * const PageWithLayout = withMainLayout<YourPageProps>(YourPageComponent);
 */
const withMainLayout: LayoutHOC = <P extends PageProps>(
  WrappedComponent: ComponentType<P>
): ComponentType<P> => {
  // Tạo component mới với displayName để dễ debug
  const WithMainLayoutComponent = (props: P) => {
    return (
      <MainLayout>
        <WrappedComponent {...props} />
      </MainLayout>
    );
  };

  // Đặt displayName để dễ debug trong React DevTools
  WithMainLayoutComponent.displayName = `withMainLayout(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  // Copy static properties từ WrappedComponent
  // Điều này quan trọng cho Next.js getStaticProps, getServerSideProps, etc.
  const wrappedComponent = WrappedComponent as any;
  const withLayoutComponent = WithMainLayoutComponent as any;

  if (wrappedComponent.getInitialProps) {
    withLayoutComponent.getInitialProps = wrappedComponent.getInitialProps;
  }

  if (wrappedComponent.getStaticProps) {
    withLayoutComponent.getStaticProps = wrappedComponent.getStaticProps;
  }

  if (wrappedComponent.getStaticPaths) {
    withLayoutComponent.getStaticPaths = wrappedComponent.getStaticPaths;
  }

  if (wrappedComponent.getServerSideProps) {
    withLayoutComponent.getServerSideProps = wrappedComponent.getServerSideProps;
  }

  return WithMainLayoutComponent;
};

export default withMainLayout;
