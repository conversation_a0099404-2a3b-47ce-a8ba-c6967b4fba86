import React from 'react';
import DashboardLayout from '../components/layouts/DashboardLayout';

/**
 * withDashboardLayout - Higher-Order Component để wrap component với DashboardLayout
 * 
 * @param {React.Component} WrappedComponent - Component cầ<PERSON> đượ<PERSON> wrap
 * @returns {React.Component} - Component đã được wrap với DashboardLayout
 * 
 * C<PERSON>ch sử dụng:
 * const PageWithDashboard = withDashboardLayout(YourPageComponent);
 * 
 * Hoặc kết hợp với MainLayout:
 * const PageWithBothLayouts = withMainLayout(withDashboardLayout(YourPageComponent));
 */
const withDashboardLayout = (WrappedComponent) => {
  // Tạo component mới với displayName để dễ debug
  const WithDashboardLayoutComponent = (props) => {
    return (
      <DashboardLayout>
        <WrappedComponent {...props} />
      </DashboardLayout>
    );
  };

  // Đặt displayName để dễ debug trong React DevTools
  WithDashboardLayoutComponent.displayName = `withDashboardLayout(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  // Copy static properties từ WrappedComponent
  // Điều này quan trọng cho Next.js getStaticProps, getServerSideProps, etc.
  if (WrappedComponent.getInitialProps) {
    WithDashboardLayoutComponent.getInitialProps = WrappedComponent.getInitialProps;
  }

  if (WrappedComponent.getStaticProps) {
    WithDashboardLayoutComponent.getStaticProps = WrappedComponent.getStaticProps;
  }

  if (WrappedComponent.getStaticPaths) {
    WithDashboardLayoutComponent.getStaticPaths = WrappedComponent.getStaticPaths;
  }

  if (WrappedComponent.getServerSideProps) {
    WithDashboardLayoutComponent.getServerSideProps = WrappedComponent.getServerSideProps;
  }

  return WithDashboardLayoutComponent;
};

export default withDashboardLayout;
