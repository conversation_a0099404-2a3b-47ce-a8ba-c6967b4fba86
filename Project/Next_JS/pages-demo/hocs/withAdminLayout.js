import React from 'react';
import AdminLayout from '../components/layouts/AdminLayout';

/**
 * withAdminLayout - Higher-Order Component để wrap component với AdminLayout
 * 
 * @param {React.Component} WrappedComponent - Component cần được wrap
 * @returns {React.Component} - Component đã được wrap với AdminLayout
 * 
 * Cách sử dụng:
 * const PageWithAdmin = withAdminLayout(YourPageComponent);
 * 
 * Hoặc kết hợp với MainLayout:
 * const PageWithBothLayouts = withMainLayout(withAdminLayout(YourPageComponent));
 */
const withAdminLayout = (WrappedComponent) => {
  // Tạo component mới với displayName để dễ debug
  const WithAdminLayoutComponent = (props) => {
    return (
      <AdminLayout>
        <WrappedComponent {...props} />
      </AdminLayout>
    );
  };

  // Đặt displayName để dễ debug trong React DevTools
  WithAdminLayoutComponent.displayName = `withAdminLayout(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  // Copy static properties từ WrappedComponent
  // Điều này quan trọng cho Next.js getStaticProps, getServerSideProps, etc.
  if (WrappedComponent.getInitialProps) {
    WithAdminLayoutComponent.getInitialProps = WrappedComponent.getInitialProps;
  }

  if (WrappedComponent.getStaticProps) {
    WithAdminLayoutComponent.getStaticProps = WrappedComponent.getStaticProps;
  }

  if (WrappedComponent.getStaticPaths) {
    WithAdminLayoutComponent.getStaticPaths = WrappedComponent.getStaticPaths;
  }

  if (WrappedComponent.getServerSideProps) {
    WithAdminLayoutComponent.getServerSideProps = WrappedComponent.getServerSideProps;
  }

  return WithAdminLayoutComponent;
};

export default withAdminLayout;
