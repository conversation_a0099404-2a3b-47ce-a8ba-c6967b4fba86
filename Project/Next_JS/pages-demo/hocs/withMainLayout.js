import React from 'react';
import MainLayout from '../components/layouts/MainLayout';

/**
 * withMainLayout - Higher-Order Component để wrap component với MainLayout
 * 
 * @param {React.Component} WrappedComponent - Component cần được wrap
 * @returns {React.Component} - Component đã được wrap với MainLayout
 * 
 * <PERSON><PERSON>ch sử dụng:
 * const PageWithLayout = withMainLayout(YourPageComponent);
 */
const withMainLayout = (WrappedComponent) => {
  // Tạo component mới với displayName để dễ debug
  const WithMainLayoutComponent = (props) => {
    return (
      <MainLayout>
        <WrappedComponent {...props} />
      </MainLayout>
    );
  };

  // Đặt displayName để dễ debug trong React DevTools
  WithMainLayoutComponent.displayName = `withMainLayout(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  // Copy static properties từ WrappedComponent
  // Điều này quan trọng cho Next.js getStaticProps, getServerSideProps, etc.
  if (WrappedComponent.getInitialProps) {
    WithMainLayoutComponent.getInitialProps = WrappedComponent.getInitialProps;
  }

  if (WrappedComponent.getStaticProps) {
    WithMainLayoutComponent.getStaticProps = WrappedComponent.getStaticProps;
  }

  if (WrappedComponent.getStaticPaths) {
    WithMainLayoutComponent.getStaticPaths = WrappedComponent.getStaticPaths;
  }

  if (WrappedComponent.getServerSideProps) {
    WithMainLayoutComponent.getServerSideProps = WrappedComponent.getServerSideProps;
  }

  return WithMainLayoutComponent;
};

export default withMainLayout;
