# 🎯 Demo Summary: Nested Layouts với HOCs trong Next.js Pages Router

## ✅ Những gì đã được tạo

### 1. Layout Components
- **MainLayout.js** - Layout chính với header, navigation, footer
- **DashboardLayout.js** - Sub-layout cho dashboard với sidebar
- **AdminLayout.js** - Sub-layout cho admin với admin-specific UI

### 2. Higher-Order Components (HOCs)
- **withMainLayout.js** - HOC để wrap components với MainLayout
- **withDashboardLayout.js** - HOC để wrap components với DashboardLayout  
- **withAdminLayout.js** - HOC để wrap components với AdminLayout
- **withMainLayout.ts** - TypeScript version của MainLayout HOC

### 3. Demo Pages
- **pages/home.js** - Trang chỉ sử dụng MainLayout
- **pages/dashboard/index.js** - Dashboard với MainLayout + DashboardLayout
- **pages/dashboard/analytics.js** - Analytics với nested layouts
- **pages/dashboard/reports.js** - Demo utility functions
- **pages/admin/index.js** - Admin với MainLayout + AdminLayout
- **pages/admin/users.js** - User management với nested layouts
- **pages/profile.js** - Demo conditional HOC

### 4. Utility Functions
- **utils/composeHOCs.js** - Utilities để compose multiple HOCs
  - `composeHOCs()` - Compose HOCs từ phải sang trái
  - `pipeHOCs()` - Pipe HOCs từ trái sang phải
  - `conditionalHOC()` - Apply HOC dựa trên condition
  - `withDashboardLayouts` - Predefined combination
  - `withAdminLayouts` - Predefined combination

### 5. TypeScript Support
- **types/layout.ts** - TypeScript types cho layouts và HOCs
- Định nghĩa interfaces cho props, components, và utility types

### 6. Styling
- **MainLayout.module.css** - Styles cho main layout
- **DashboardLayout.module.css** - Styles cho dashboard layout
- **AdminLayout.module.css** - Styles cho admin layout
- **Page.module.css** - Shared styles cho pages với responsive design

## 🔧 Patterns được demo

### 1. Basic HOC Usage
```javascript
// Trang đơn giản với chỉ MainLayout
export default withMainLayout(HomePage);
```

### 2. HOC Composition
```javascript
// Nested layouts với composition
export default withMainLayout(withDashboardLayout(DashboardPage));
```

### 3. Utility Functions
```javascript
// Sử dụng utility để compose HOCs
export default withDashboardLayouts(ReportsPage);
```

### 4. Conditional HOCs
```javascript
// Apply layout dựa trên condition
const withConditionalAdmin = conditionalHOC(
  (props) => props.user?.role === 'admin',
  withAdminLayout
);
export default composeHOCs(withMainLayout, withConditionalAdmin)(ProfilePage);
```

### 5. TypeScript Integration
```typescript
// Type-safe HOCs
const withMainLayout: LayoutHOC = <P extends PageProps>(
  WrappedComponent: ComponentType<P>
): ComponentType<P> => { /* ... */ };
```

## 🎨 UI Features

### MainLayout
- Header với navigation links
- Footer với company links
- Responsive design
- Consistent branding

### DashboardLayout  
- Dashboard header với breadcrumbs
- Sidebar navigation với icons
- Grid layout cho content
- Dashboard-specific styling

### AdminLayout
- Admin header với security badge
- Admin sidebar với admin tools
- Red color scheme để phân biệt
- Admin-specific navigation

## 📱 Responsive Design

Tất cả layouts đều responsive:
- **Desktop**: Full sidebar và navigation
- **Mobile**: Collapsed navigation, stacked layouts
- **Tablet**: Adaptive layouts

## 🔍 Key Benefits Demonstrated

1. **Reusability**: Layouts có thể được sử dụng cho nhiều pages
2. **Composition**: Dễ dàng kết hợp multiple layouts
3. **Flexibility**: Conditional layouts dựa trên props/state
4. **Type Safety**: TypeScript support cho better DX
5. **Performance**: Layouts chỉ render khi cần
6. **Maintainability**: Separation of concerns rõ ràng
7. **Scalability**: Dễ dàng thêm layouts mới

## 🚀 How to Test

1. **Start the server**: `npm run dev`
2. **Visit pages**:
   - `/home` - Basic layout
   - `/dashboard` - Nested dashboard layout
   - `/admin` - Nested admin layout
   - `/profile` - Conditional layout demo
3. **Check responsive**: Resize browser window
4. **Inspect DevTools**: Xem component hierarchy

## 📚 Educational Value

Demo này dạy:
- HOCs pattern trong React
- Layout composition strategies
- Next.js Pages Router best practices
- TypeScript integration
- CSS Modules usage
- Responsive design principles
- Code organization patterns

## 🎯 Next Steps

Có thể mở rộng với:
- Authentication-based layouts
- Theme switching
- Layout animations
- Performance optimizations
- Testing strategies
- Accessibility improvements

---

**Kết luận**: Demo này cung cấp một foundation vững chắc cho việc implement nested layouts trong Next.js Pages Router sử dụng HOCs pattern, với code quality cao và educational value tốt.
