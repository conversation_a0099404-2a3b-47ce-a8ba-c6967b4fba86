# Nested Layouts với Higher-Order Components (HOCs) trong Next.js Pages Router

Dự án này demo cách implement nested layouts trong Next.js Pages Router sử dụng Higher-Order Components (HOCs) pattern.

## 🏗️ Cấu trúc dự án

```
├── components/
│   └── layouts/
│       ├── MainLayout.js          # Layout chính cho toàn bộ app
│       ├── DashboardLayout.js     # Sub-layout cho dashboard
│       └── AdminLayout.js         # Sub-layout cho admin area
├── hocs/
│   ├── withMainLayout.js          # HOC cho MainLayout
│   ├── withDashboardLayout.js     # HOC cho DashboardLayout
│   └── withAdminLayout.js         # HOC cho AdminLayout
├── pages/
│   ├── home.js                    # Trang chỉ dùng MainLayoutd
│   ├── dashboard/
│   │   ├── index.js              # Dashboard với nested layouts
│   │   └── analytics.js          # Analytics với nested layouts
│   └── admin/
│       ├── index.js              # Admin với nested layouts
│       └── users.js              # User management với nested layouts
└── styles/
    ├── MainLayout.module.css      # Styles cho MainLayout
    ├── DashboardLayout.module.css # Styles cho DashboardLayout
    ├── AdminLayout.module.css     # Styles cho AdminLayout
    └── Page.module.css           # Shared styles cho pages
```

## 🎯 Các loại Layout

### 1. MainLayout
- **Mục đích**: Layout chính cho toàn bộ ứng dụng
- **Bao gồm**: Header, Navigation, Footer
- **Sử dụng**: Tất cả các trang

### 2. DashboardLayout  
- **Mục đích**: Sub-layout cho khu vực dashboard
- **Bao gồm**: Dashboard header, Sidebar navigation
- **Sử dụng**: Các trang dashboard

### 3. AdminLayout
- **Mục đích**: Sub-layout cho khu vực admin
- **Bao gồm**: Admin header, Admin sidebar, Admin badge
- **Sử dụng**: Các trang admin

## 🔧 Higher-Order Components (HOCs)

### Cách hoạt động
HOCs là functions nhận vào một component và trả về component mới đã được wrap với layout:

```javascript
const withMainLayout = (WrappedComponent) => {
  const WithMainLayoutComponent = (props) => {
    return (
      <MainLayout>
        <WrappedComponent {...props} />
      </MainLayout>
    );
  };
  return WithMainLayoutComponent;
};
```

### Composition Pattern
Để tạo nested layouts, chúng ta compose nhiều HOCs:

```javascript
// Chỉ MainLayout
export default withMainLayout(HomePage);

// MainLayout + DashboardLayout
export default withMainLayout(withDashboardLayout(DashboardPage));

// MainLayout + AdminLayout  
export default withMainLayout(withAdminLayout(AdminPage));
```

## 📄 Ví dụ sử dụng

### 1. Trang đơn giản (chỉ MainLayout)
```javascript
// pages/home.js
import withMainLayout from '../hocs/withMainLayout';

const HomePage = () => {
  return <div>Home content</div>;
};

export default withMainLayout(HomePage);
```

### 2. Trang Dashboard (MainLayout + DashboardLayout)
```javascript
// pages/dashboard/index.js
import withMainLayout from '../../hocs/withMainLayout';
import withDashboardLayout from '../../hocs/withDashboardLayout';

const DashboardPage = () => {
  return <div>Dashboard content</div>;
};

export default withMainLayout(withDashboardLayout(DashboardPage));
```

### 3. Trang Admin (MainLayout + AdminLayout)
```javascript
// pages/admin/index.js
import withMainLayout from '../../hocs/withMainLayout';
import withAdminLayout from '../../hocs/withAdminLayout';

const AdminPage = () => {
  return <div>Admin content</div>;
};

export default withMainLayout(withAdminLayout(AdminPage));
```

## 🎨 Styling

Mỗi layout có file CSS module riêng:
- `MainLayout.module.css` - Styles cho header, footer, navigation
- `DashboardLayout.module.css` - Styles cho dashboard sidebar và layout
- `AdminLayout.module.css` - Styles cho admin sidebar và layout
- `Page.module.css` - Shared styles cho page content

## 🚀 Chạy dự án

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 🌐 Demo Pages

1. **Home** (`/home`) - Chỉ MainLayout
2. **Dashboard** (`/dashboard`) - MainLayout + DashboardLayout
3. **Analytics** (`/dashboard/analytics`) - MainLayout + DashboardLayout
4. **Admin** (`/admin`) - MainLayout + AdminLayout
5. **User Management** (`/admin/users`) - MainLayout + AdminLayout

## ✨ Ưu điểm của HOCs Pattern

1. **Tái sử dụng**: Layouts có thể được sử dụng cho nhiều pages
2. **Composition**: Dễ dàng kết hợp nhiều layouts
3. **Separation of Concerns**: Logic layout tách biệt khỏi page content
4. **Flexibility**: Dễ dàng thêm/bớt layouts cho từng page
5. **Type Safety**: Hỗ trợ TypeScript tốt
6. **Next.js Compatible**: Hoạt động tốt với getStaticProps, getServerSideProps

## 🔍 Lưu ý quan trọng

1. **Thứ tự HOCs**: MainLayout luôn ở ngoài cùng
2. **Static Methods**: HOCs preserve Next.js static methods
3. **DisplayName**: Mỗi HOC có displayName để debug dễ dàng
4. **Props Forwarding**: Tất cả props được forward đến wrapped component
5. **Responsive**: Tất cả layouts đều responsive

## 📚 Tài liệu tham khảo

- [Next.js Pages Router](https://nextjs.org/docs/pages)
- [React Higher-Order Components](https://reactjs.org/docs/higher-order-components.html)
- [CSS Modules](https://github.com/css-modules/css-modules)
