/**
 * Utility functions để compose multiple Higher-Order Components
 * <PERSON><PERSON><PERSON><PERSON> việc kết hợp nhiều HOCs trở nên dễ dàng và readable hơn
 */

/**
 * Compose multiple HOCs into a single HOC
 * 
 * @param {...Function} hocs - <PERSON><PERSON> sách các HOCs cần compose
 * @returns {Function} - HOC đã được compose
 * 
 * C<PERSON>ch sử dụng:
 * const withLayouts = composeHOCs(withMainLayout, withDashboardLayout);
 * export default withLayouts(YourComponent);
 * 
 * Thay vì:
 * export default withMainLayout(withDashboardLayout(YourComponent));
 */
export const composeHOCs = (...hocs) => {
  return (WrappedComponent) => {
    return hocs.reduceRight((acc, hoc) => hoc(acc), WrappedComponent);
  };
};

/**
 * Pipe HOCs từ trái sang phải (ngượ<PERSON> với compose)
 * 
 * @param {...Function} hocs - <PERSON><PERSON> sách các HOCs cần pipe
 * @returns {Function} - <PERSON><PERSON> đ<PERSON> được pipe
 * 
 * Cách sử dụng:
 * const withLayouts = pipeHOCs(withMainLayout, withDashboardLayout);
 * export default withLayouts(YourComponent);
 */
export const pipeHOCs = (...hocs) => {
  return (WrappedComponent) => {
    return hocs.reduce((acc, hoc) => hoc(acc), WrappedComponent);
  };
};

/**
 * Tạo HOC với conditional logic
 * 
 * @param {Function} condition - Function để check condition
 * @param {Function} hoc - HOC sẽ được apply nếu condition = true
 * @returns {Function} - Conditional HOC
 * 
 * Cách sử dụng:
 * const withConditionalAdmin = conditionalHOC(
 *   (props) => props.user?.role === 'admin',
 *   withAdminLayout
 * );
 */
export const conditionalHOC = (condition, hoc) => {
  return (WrappedComponent) => {
    const ConditionalComponent = (props) => {
      if (condition(props)) {
        const EnhancedComponent = hoc(WrappedComponent);
        return <EnhancedComponent {...props} />;
      }
      return <WrappedComponent {...props} />;
    };

    ConditionalComponent.displayName = `conditionalHOC(${
      WrappedComponent.displayName || WrappedComponent.name || 'Component'
    })`;

    return ConditionalComponent;
  };
};

/**
 * Predefined layout combinations cho convenience
 */

// Main + Dashboard layouts
export const withDashboardLayouts = composeHOCs(
  require('../hocs/withMainLayout').default,
  require('../hocs/withDashboardLayout').default
);

// Main + Admin layouts  
export const withAdminLayouts = composeHOCs(
  require('../hocs/withMainLayout').default,
  require('../hocs/withAdminLayout').default
);

// Chỉ Main layout
export const withMainLayoutOnly = require('../hocs/withMainLayout').default;

/**
 * Factory function để tạo layout combinations
 * 
 * @param {Object} layouts - Object chứa các layout HOCs
 * @returns {Object} - Object chứa các layout combinations
 * 
 * Cách sử dụng:
 * const layouts = createLayoutCombinations({
 *   main: withMainLayout,
 *   dashboard: withDashboardLayout,
 *   admin: withAdminLayout
 * });
 * 
 * export default layouts.mainAndDashboard(YourComponent);
 */
export const createLayoutCombinations = (layouts) => {
  return {
    // Single layouts
    main: layouts.main,
    dashboard: layouts.dashboard,
    admin: layouts.admin,
    
    // Combinations
    mainAndDashboard: composeHOCs(layouts.main, layouts.dashboard),
    mainAndAdmin: composeHOCs(layouts.main, layouts.admin),
    
    // All layouts (nếu cần)
    all: composeHOCs(layouts.main, layouts.dashboard, layouts.admin),
    
    // Custom combination
    custom: (...selectedLayouts) => composeHOCs(...selectedLayouts)
  };
};

/**
 * Debug utility để log HOC composition
 */
export const debugHOC = (name) => {
  return (WrappedComponent) => {
    const DebugComponent = (props) => {
      console.log(`[HOC Debug] ${name} rendered with props:`, props);
      return <WrappedComponent {...props} />;
    };

    DebugComponent.displayName = `debugHOC(${name})`;
    return DebugComponent;
  };
};

// Example usage:
// export default composeHOCs(
//   debugHOC('MainLayout'),
//   withMainLayout,
//   debugHOC('DashboardLayout'), 
//   withDashboardLayout,
//   debugHOC('FinalComponent')
// )(YourComponent);
