/* DashboardLayout.module.css - Styles cho DashboardLayout component */

.dashboardContainer {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Dashboard Header */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.dashboardTitle {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.breadcrumbLink {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumbLink:hover {
  color: #1d4ed8;
}

.breadcrumbSeparator {
  color: #9ca3af;
}

.breadcrumbCurrent {
  font-weight: 500;
}

/* Dashboard Content Layout */
.dashboardContent {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
  min-height: 500px;
}

/* Sidebar Styles */
.sidebar {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.sidebarList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebarItem {
  margin-bottom: 0.5rem;
}

.sidebarLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  color: #6b7280;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.sidebarLink:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.sidebarIcon {
  font-size: 1.125rem;
}

/* Content Area */
.content {
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 1rem;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .dashboardContent {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    order: 2;
  }

  .content {
    order: 1;
  }
}
