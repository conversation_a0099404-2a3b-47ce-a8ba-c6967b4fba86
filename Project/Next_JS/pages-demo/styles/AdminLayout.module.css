/* AdminLayout.module.css - Styles cho AdminLayout component */

.adminContainer {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Admin <PERSON>er */
.adminHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 0.5rem;
  color: white;
}

.adminTitle {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.adminIcon {
  font-size: 1.5rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #fca5a5;
}

.breadcrumbLink {
  color: #fecaca;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumbLink:hover {
  color: white;
}

.breadcrumbSeparator {
  color: #fca5a5;
}

.breadcrumbCurrent {
  font-weight: 500;
  color: white;
}

.adminBadge {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Admin Content Layout */
.adminContent {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  min-height: 500px;
}

/* Sidebar Styles */
.sidebar {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #dc2626;
  height: fit-content;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 1rem 0;
}

.sidebarList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebarItem {
  margin-bottom: 0.5rem;
}

.sidebarLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  color: #6b7280;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.sidebarLink:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

.sidebarIcon {
  font-size: 1.125rem;
}

/* Content Area */
.content {
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .adminContainer {
    padding: 1rem;
  }

  .adminHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .adminTitle {
    font-size: 1.5rem;
  }

  .adminContent {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    order: 2;
  }

  .content {
    order: 1;
  }
}
