/* MainLayout.module.css - Styles cho MainLayout component */

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  background-color: #1f2937;
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

.logo a {
  color: white;
  text-decoration: none;
  transition: color 0.2s;
}

.logo a:hover {
  color: #60a5fa;
}

.nav {
  display: flex;
  gap: 2rem;
}

.navLink {
  color: #d1d5db;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.navLink:hover {
  color: white;
  background-color: #374151;
}

/* Main Content Styles */
.main {
  flex: 1;
  background-color: #f9fafb;
  min-height: calc(100vh - 140px); /* Adjust based on header/footer height */
}

/* Footer Styles */
.footer {
  background-color: #111827;
  color: #9ca3af;
  padding: 2rem 0;
  margin-top: auto;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footerLinks {
  display: flex;
  gap: 1.5rem;
}

.footerLink {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s;
}

.footerLink:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 1rem;
  }

  .nav {
    gap: 1rem;
  }

  .footerContent {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footerLinks {
    gap: 1rem;
  }
}
