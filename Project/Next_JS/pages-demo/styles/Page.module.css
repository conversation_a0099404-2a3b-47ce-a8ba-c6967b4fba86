/* Page.module.css - Shared styles cho các pages */

/* Page Container */
.pageContainer {
  padding: 0;
}

.pageHeader {
  margin-bottom: 2rem;
}

.pageTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.pageSubtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
}

.pageContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.cardDescription {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Feature List */
.featureList {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.featureList li {
  padding: 0.5rem 0;
  color: #374151;
}

/* Code Example */
.codeExample {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.codeExample code {
  color: #1f2937;
  font-size: 0.875rem;
}

/* Button Styles */
.button {
  display: inline-block;
  background-color: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #2563eb;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.primaryButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primaryButton:hover {
  background-color: #2563eb;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.statCard {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  text-align: center;
}

.statTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.statChange {
  font-size: 0.875rem;
  color: #059669;
  margin: 0;
}

/* Chart Styles */
.chartGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chartCard {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chartTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.chartPlaceholder {
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 0.375rem;
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.chartData {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
  font-size: 0.875rem;
}

/* Insights */
.insightsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
}

.insightIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.insight h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.insight p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Admin Specific Styles */
.adminGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.adminCard {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-left: 4px solid #dc2626;
}

.adminCardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.adminCardDescription {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.adminStats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.adminButton {
  display: inline-block;
  background-color: #dc2626;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.adminButton:hover {
  background-color: #b91c1c;
}

/* Alerts */
.alertsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  border-left: 4px solid #6b7280;
}

.alertIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.alert h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-size: 0.875rem;
}

.alert p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.alert small {
  color: #9ca3af;
  font-size: 0.75rem;
}

/* Table Styles */
.tableContainer {
  overflow-x: auto;
  margin: 1rem 0;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.table th,
.table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.table td {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Badges and Status */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badgeAdmin {
  background-color: #fef2f2;
  color: #dc2626;
}

.badgeUser {
  background-color: #f0f9ff;
  color: #0369a1;
}

.badgeModerator {
  background-color: #fefce8;
  color: #ca8a04;
}

.status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.statusActive {
  background-color: #f0fdf4;
  color: #166534;
}

.statusInactive {
  background-color: #fef2f2;
  color: #dc2626;
}

.statusPending {
  background-color: #fefce8;
  color: #ca8a04;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.editButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton:hover {
  background-color: #2563eb;
}

.deleteButton {
  background-color: #dc2626;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: #b91c1c;
}

/* Tools Grid */
.toolsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.toolButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  color: #374151;
}

.toolButton:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.toolIcon {
  font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pageTitle {
    font-size: 2rem;
  }

  .card {
    padding: 1.5rem;
  }

  .buttonGroup {
    flex-direction: column;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .chartGrid {
    grid-template-columns: 1fr;
  }

  .adminGrid {
    grid-template-columns: 1fr;
  }

  .toolsGrid {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    flex-direction: column;
  }
}
