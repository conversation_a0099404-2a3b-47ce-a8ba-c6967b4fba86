import React from 'react';
import Link from 'next/link';
import styles from '../../styles/DashboardLayout.module.css';

/**
 * DashboardLayout - Sub-layout cho khu vực dashboard
 * Bao gồm sidebar navigation và content area cho dashboard
 */
const DashboardLayout = ({ children }) => {
  return (
    <div className={styles.dashboardContainer}>
      {/* Dashboard Header */}
      <div className={styles.dashboardHeader}>
        <h2 className={styles.dashboardTitle}>Dashboard</h2>
        <div className={styles.breadcrumb}>
          <Link href="/home" className={styles.breadcrumbLink}>
            Home
          </Link>
          <span className={styles.breadcrumbSeparator}>/</span>
          <span className={styles.breadcrumbCurrent}>Dashboard</span>
        </div>
      </div>

      <div className={styles.dashboardContent}>
        {/* Sidebar Navigation */}
        <aside className={styles.sidebar}>
          <nav className={styles.sidebarNav}>
            <h3 className={styles.sidebarTitle}>Dashboard Menu</h3>
            <ul className={styles.sidebarList}>
              <li className={styles.sidebarItem}>
                <Link href="/dashboard" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>📊</span>
                  Overview
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/dashboard/analytics" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>📈</span>
                  Analytics
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/dashboard/reports" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>📋</span>
                  Reports
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/dashboard/settings" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>⚙️</span>
                  Settings
                </Link>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Main Dashboard Content */}
        <div className={styles.content}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
