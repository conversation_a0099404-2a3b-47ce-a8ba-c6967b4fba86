import React from 'react';
import Link from 'next/link';
import styles from '../../styles/AdminLayout.module.css';

/**
 * AdminLayout - Sub-layout cho khu vực admin
 * Bao gồm admin navigation và content area với quyền admin
 */
const AdminLayout = ({ children }) => {
  return (
    <div className={styles.adminContainer}>
      {/* Admin Header */}
      <div className={styles.adminHeader}>
        <h2 className={styles.adminTitle}>
          <span className={styles.adminIcon}>🔐</span>
          Admin Panel
        </h2>
        <div className={styles.breadcrumb}>
          <Link href="/home" className={styles.breadcrumbLink}>
            Home
          </Link>
          <span className={styles.breadcrumbSeparator}>/</span>
          <span className={styles.breadcrumbCurrent}>Admin</span>
        </div>
        <div className={styles.adminBadge}>
          Admin Access
        </div>
      </div>

      <div className={styles.adminContent}>
        {/* Admin Sidebar Navigation */}
        <aside className={styles.sidebar}>
          <nav className={styles.sidebarNav}>
            <h3 className={styles.sidebarTitle}>Admin Tools</h3>
            <ul className={styles.sidebarList}>
              <li className={styles.sidebarItem}>
                <Link href="/admin" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>🏠</span>
                  Admin Home
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/admin/users" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>👥</span>
                  User Management
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/admin/content" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>📝</span>
                  Content Management
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/admin/system" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>🔧</span>
                  System Settings
                </Link>
              </li>
              <li className={styles.sidebarItem}>
                <Link href="/admin/logs" className={styles.sidebarLink}>
                  <span className={styles.sidebarIcon}>📜</span>
                  System Logs
                </Link>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Main Admin Content */}
        <div className={styles.content}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
