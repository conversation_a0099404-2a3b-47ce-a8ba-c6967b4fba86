import React from 'react';
import Link from 'next/link';
import styles from '../../styles/MainLayout.module.css';

/**
 * MainLayout - Layout chính cho toàn bộ ứng dụng
 * Bao gồm header, navigation, footer và content area
 */
const MainLayout = ({ children }) => {
  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.logo}>
            <Link href="/">MyApp</Link>
          </h1>
          <nav className={styles.nav}>
            <Link href="/home" className={styles.navLink}>
              Home
            </Link>
            <Link href="/dashboard" className={styles.navLink}>
              Dashboard
            </Link>
            <Link href="/admin" className={styles.navLink}>
              Admin
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content Area */}
      <main className={styles.main}>
        {children}
      </main>

      {/* Footer */}
      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <p>&copy; 2024 MyApp. All rights reserved.</p>
          <div className={styles.footerLinks}>
            <Link href="/about" className={styles.footerLink}>
              About
            </Link>
            <Link href="/contact" className={styles.footerLink}>
              Contact
            </Link>
            <Link href="/privacy" className={styles.footerLink}>
              Privacy
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;
